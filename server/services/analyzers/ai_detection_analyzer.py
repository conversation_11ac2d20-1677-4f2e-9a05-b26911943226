"""
Analyzer for AI-generated code detection.
"""
import re
from typing import Dict, Any, List

from server.services.analyzers.base_analyzer import BaseAnalyzer


class AIDetectionResult:
    """AI detection result."""

    def __init__(self, ai_generated_probability: float, detection_method: str, flagged_patterns: List[str] = None):
        """Initialize an AI detection result.

        Args:
            ai_generated_probability: Probability that the code was generated by AI
            detection_method: Method used for detection
            flagged_patterns: Patterns that were flagged as indicative of AI generation
        """
        self.ai_generated_probability = ai_generated_probability
        self.detection_method = detection_method
        self.flagged_patterns = flagged_patterns or []

    def model_dump(self) -> Dict[str, Any]:
        """Convert the result to a dictionary.

        Returns:
            Dictionary representation of the result
        """
        return {
            "ai_generated_probability": self.ai_generated_probability,
            "detection_method": self.detection_method,
            "flagged_patterns": self.flagged_patterns
        }


class AIDetectionAnalyzer(BaseAnalyzer):
    """Analyzer for AI-generated code detection."""

    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code to detect if it was generated by AI.

        Args:
            data: Data to analyze, including code

        Returns:
            Analysis results with AI detection probability and flagged patterns
        """
        language = data.get("language", "").lower()
        code = data.get("submitted_code", "")

        if not code:
            return {
                "ai_detection": AIDetectionResult(
                    ai_generated_probability=0.0,
                    detection_method="pattern_matching",
                    flagged_patterns=[]
                ).model_dump()
            }

        # Detect AI-generated code based on language
        if language == "python":
            result = self._detect_ai_python_code(code, data)
        elif language == "javascript":
            result = self._detect_ai_javascript_code(code)
        elif language == "java":
            result = self._detect_ai_java_code(code)
        else:
            # Default to Python if language not supported
            result = self._detect_ai_python_code(code, data)

        return {
            "ai_detection": result.model_dump()
        }

    def _detect_ai_python_code(self, code: str, data: Dict[str, Any]) -> AIDetectionResult:
        """Detect AI-generated Python code.

        Args:
            code: Python code to analyze
            data: Additional data for analysis

        Returns:
            AI detection result
        """
        # Define patterns that are common in AI-generated code
        patterns = {
            "verbose_comments": r"# This function .* does the following",
            "step_comments": r"# Step \d+:",
            "explanation_comments": r"# Let me explain",
            "common_ai_phrases": r"(let's|we can|first|finally|now)",
            "excessive_docstrings": r'""".*?"""',
            "perfect_formatting": r"def\s+\w+\(.*?\):\s+\"\"\".*?\"\"\"\s+",
            "generic_variable_names": r"\b(result|output|temp|data|value|item|element|arr|lst|dict|obj)\b"
        }

        # Check for each pattern
        flagged_patterns = []
        pattern_scores = {}

        for name, pattern in patterns.items():
            matches = re.findall(pattern, code, re.IGNORECASE | re.DOTALL)
            if matches:
                flagged_patterns.append(f"{name}: {len(matches)} instances")
                pattern_scores[name] = min(len(matches) * 0.1, 0.5)  # Cap at 0.5

        # Check for code similarity to starter code
        # This is a simplified check - in a real system, you'd use more sophisticated methods
        try:
            starter_code = data.get("starter_code", "")
            if starter_code and len(code) > len(starter_code) * 3:
                flagged_patterns.append("code_length: significantly longer than starter code")
                pattern_scores["code_length"] = 0.3
            elif not starter_code:
                # If starter code is not provided, use a default score
                pattern_scores["code_length"] = 0.1
        except Exception:
            # If there's any error, use a default score
            pattern_scores["code_length"] = 0.1

        # Calculate overall probability
        if pattern_scores:
            ai_probability = min(sum(pattern_scores.values()), 0.95)  # Cap at 0.95
        else:
            ai_probability = 0.1  # Base probability

        return AIDetectionResult(
            ai_generated_probability=ai_probability,
            detection_method="pattern_matching",
            flagged_patterns=flagged_patterns
        )

    def _detect_ai_javascript_code(self, code: str) -> AIDetectionResult:
        """Detect AI-generated JavaScript code.

        Args:
            code: JavaScript code to analyze

        Returns:
            AI detection result
        """
        # Define patterns that are common in AI-generated JavaScript code
        patterns = {
            "verbose_comments": r"// This function .* does the following",
            "step_comments": r"// Step \d+:",
            "explanation_comments": r"// Let me explain",
            "common_ai_phrases": r"(let's|we can|first|finally|now)",
            "jsdoc_comments": r"/\*\*.*?\*/",
            "perfect_formatting": r"function\s+\w+\(.*?\)\s+{\s+/\*\*.*?\*/\s+",
            "generic_variable_names": r"\b(result|output|temp|data|value|item|element|arr|obj)\b"
        }

        # Check for each pattern
        flagged_patterns = []
        pattern_scores = {}

        for name, pattern in patterns.items():
            matches = re.findall(pattern, code, re.IGNORECASE | re.DOTALL)
            if matches:
                flagged_patterns.append(f"{name}: {len(matches)} instances")
                pattern_scores[name] = min(len(matches) * 0.1, 0.5)  # Cap at 0.5

        # Add a default score for code length
        pattern_scores["code_length"] = 0.1

        # Calculate overall probability
        if pattern_scores:
            ai_probability = min(sum(pattern_scores.values()), 0.95)  # Cap at 0.95
        else:
            ai_probability = 0.1  # Base probability

        return AIDetectionResult(
            ai_generated_probability=ai_probability,
            detection_method="pattern_matching",
            flagged_patterns=flagged_patterns
        )

    def _detect_ai_java_code(self, code: str) -> AIDetectionResult:
        """Detect AI-generated Java code.

        Args:
            code: Java code to analyze

        Returns:
            AI detection result
        """
        # Define patterns that are common in AI-generated Java code
        patterns = {
            "verbose_comments": r"// This (method|function) .* does the following",
            "step_comments": r"// Step \d+:",
            "explanation_comments": r"// Let me explain",
            "common_ai_phrases": r"(let's|we can|first|finally|now)",
            "javadoc_comments": r"/\*\*.*?\*/",
            "perfect_formatting": r"(public|private)\s+\w+\s+\w+\(.*?\)\s+{\s+",
            "generic_variable_names": r"\b(result|output|temp|data|value|item|element|arr|list|map|obj)\b"
        }

        # Check for each pattern
        flagged_patterns = []
        pattern_scores = {}

        for name, pattern in patterns.items():
            matches = re.findall(pattern, code, re.IGNORECASE | re.DOTALL)
            if matches:
                flagged_patterns.append(f"{name}: {len(matches)} instances")
                pattern_scores[name] = min(len(matches) * 0.1, 0.5)  # Cap at 0.5

        # Add a default score for code length
        pattern_scores["code_length"] = 0.1

        # Calculate overall probability
        if pattern_scores:
            ai_probability = min(sum(pattern_scores.values()), 0.95)  # Cap at 0.95
        else:
            ai_probability = 0.1  # Base probability

        return AIDetectionResult(
            ai_generated_probability=ai_probability,
            detection_method="pattern_matching",
            flagged_patterns=flagged_patterns
        )
