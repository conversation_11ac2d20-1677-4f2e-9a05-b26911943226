#!/usr/bin/env python3
"""
API Server for Assessment Analysis System
"""

import os
import logging
import json
import uuid
from datetime import datetime, timedelta
from functools import wraps

from flask import Flask, request, jsonify, g, send_from_directory
from flask_cors import CORS
import jwt

from server.services.database_service import DatabaseService
from server.services.analysis_service import AnalysisService
from server.services.reporting_service import ReportingService
from server.services.background_worker import BackgroundWorker
from server.swagger_new import spec, swagger_ui_blueprint, SWAGGER_URL
from server.utils.json_encoder import MongoJSONEncoder

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configure app to use custom JSON encoder for MongoDB ObjectId
app.json_encoder = MongoJSONEncoder

# Register Swagger UI blueprint
app.register_blueprint(swagger_ui_blueprint, url_prefix=SWAGGER_URL)

# Load configuration
app.config["SECRET_KEY"] = os.getenv("SECRET_KEY", "dev_secret_key")
app.config["JWT_EXPIRATION_DELTA"] = int(os.getenv("JWT_EXPIRATION_DELTA", 86400))  # 24 hours in seconds

# Initialize services
db_service = DatabaseService()
analysis_service = AnalysisService(db_service)
reporting_service = ReportingService(db_service)
background_worker = BackgroundWorker(db_service, analysis_service)

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check if token is in headers
        if "Authorization" in request.headers:
            auth_header = request.headers["Authorization"]
            if auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]

        if not token:
            return jsonify({"message": "Token is missing"}), 401

        try:
            # Decode token
            data = jwt.decode(token, app.config["SECRET_KEY"], algorithms=["HS256"])
            g.user = data["user"]
        except jwt.ExpiredSignatureError:
            return jsonify({"message": "Token has expired"}), 401
        except jwt.InvalidTokenError:
            return jsonify({"message": "Invalid token"}), 401

        return f(*args, **kwargs)

    return decorated

# Routes

@app.route("/api/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    })

@app.route("/api/login", methods=["POST"])
def login():
    """Login endpoint."""
    data = request.get_json()

    if not data or not data.get("username") or not data.get("password"):
        return jsonify({"message": "Missing username or password"}), 400

    # For demo purposes, accept any username/password
    # In production, you would validate against a user database
    username = data.get("username")

    # Generate token
    token = jwt.encode({
        "user": username,
        "exp": datetime.utcnow() + timedelta(seconds=app.config["JWT_EXPIRATION_DELTA"])
    }, app.config["SECRET_KEY"], algorithm="HS256")

    return jsonify({
        "token": token,
        "expires_in": app.config["JWT_EXPIRATION_DELTA"]
    })

# Assessment endpoints

@app.route("/api/assessments", methods=["GET"])
def get_assessments():
    """Get all assessments."""
    assessments = db_service.get_all_assessments()
    return jsonify(assessments)

@app.route("/api/assessments/<test_id>", methods=["GET"])
def get_assessment(test_id):
    """Get a specific assessment."""
    assessment = db_service.get_assessment_by_id(test_id)

    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    return jsonify(assessment)

@app.route("/api/assessments", methods=["POST"])
def create_assessment():
    """Create a new assessment."""
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    # Ensure testId is present
    if "testId" not in data:
        return jsonify({"message": "testId is required"}), 400

    # Check if assessment already exists
    existing = db_service.get_assessment_by_id(data["testId"])
    if existing:
        return jsonify({"message": "Assessment with this ID already exists"}), 409

    # Add timestamps
    if "createdAt" not in data:
        data["createdAt"] = datetime.now().isoformat()
    if "updatedAt" not in data:
        data["updatedAt"] = datetime.now().isoformat()

    # Store assessment
    assessment_id = db_service.store_assessment(data)

    return jsonify({
        "message": "Assessment created successfully",
        "assessment_id": assessment_id
    }), 201

@app.route("/api/assessments/<test_id>", methods=["PUT"])
def update_assessment(test_id):
    """Update an existing assessment."""
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    # Check if assessment exists
    existing = db_service.get_assessment_by_id(test_id)
    if not existing:
        return jsonify({"message": "Assessment not found"}), 404

    # Update timestamp
    data["updatedAt"] = datetime.now().isoformat()

    # Update assessment
    db_service.update_assessment(test_id, data)

    return jsonify({
        "message": "Assessment updated successfully"
    })

@app.route("/api/assessments/<test_id>", methods=["DELETE"])
def delete_assessment(test_id):
    """Delete an assessment."""
    # Check if assessment exists
    existing = db_service.get_assessment_by_id(test_id)
    if not existing:
        return jsonify({"message": "Assessment not found"}), 404

    # Delete assessment
    db_service.delete_assessment(test_id)

    return jsonify({
        "message": "Assessment deleted successfully"
    })

# Solution endpoints

@app.route("/api/solutions", methods=["GET"])
def get_solutions():
    """Get all solutions."""
    solutions = db_service.get_all_solutions()
    return jsonify(solutions)

@app.route("/api/solutions/<solution_id>", methods=["GET"])
def get_solution(solution_id):
    """Get a specific solution."""
    solution = db_service.get_solution_by_id(solution_id)

    if not solution:
        return jsonify({"message": "Solution not found"}), 404

    return jsonify(solution)

@app.route("/api/assessments/<test_id>/solutions", methods=["GET"])
def get_solutions_by_test(test_id):
    """Get all solutions for a specific test."""
    solutions = db_service.get_solutions_by_test_id(test_id)
    return jsonify(solutions)

@app.route("/api/solutions", methods=["POST"])
def create_solution():
    """Create a new solution."""
    data = request.get_json()

    if not data:
        return jsonify({"message": "No data provided"}), 400

    # Ensure required fields are present
    if "solution_id" not in data:
        return jsonify({"message": "solution_id is required"}), 400
    if "test_id" not in data:
        return jsonify({"message": "test_id is required"}), 400

    # Check if solution already exists
    existing = db_service.get_solution_by_id(data["solution_id"])
    if existing:
        return jsonify({"message": "Solution with this ID already exists"}), 409

    # Store solution
    solution_id = db_service.store_solution(data)

    return jsonify({
        "message": "Solution created successfully",
        "solution_id": solution_id
    }), 201

# Analysis endpoints

@app.route("/api/analysis/<solution_id>", methods=["GET"])
def get_analysis(solution_id):
    """Get analysis for a specific solution."""
    analysis = db_service.get_analysis_by_solution_id(solution_id)

    if not analysis:
        return jsonify({"message": "Analysis not found"}), 404

    return jsonify(analysis)

@app.route("/api/analyze/solution/<solution_id>", methods=["POST"])
def analyze_solution(solution_id):
    """Analyze a specific solution asynchronously."""
    # Get solution
    solution = db_service.get_solution_by_id(solution_id)
    if not solution:
        return jsonify({"message": "Solution not found"}), 404

    # Check if solution is already being analyzed
    existing_jobs = db_service.get_all_analysis_jobs()
    for job in existing_jobs:
        if (job["job_type"] == "solution" and
            job["job_data"].get("solution_id") == solution_id and
            job["status"] in ["pending", "running"]):
            return jsonify({
                "message": "Solution is already being analyzed",
                "job_id": job["job_id"],
                "status": job["status"]
            })

    # Check if solution is already analyzed
    existing_analysis = db_service.get_analysis_by_solution_id(solution_id)
    if existing_analysis:
        return jsonify({
            "message": "Solution is already analyzed",
            "analysis_id": existing_analysis.get("analysis_id") or existing_analysis.get("_id")
        })

    # Start analysis job
    job_id = background_worker.start_analysis_job("solution", {"solution_id": solution_id})

    return jsonify({
        "message": "Solution analysis started",
        "job_id": job_id
    })

@app.route("/api/analyze/test/<test_id>", methods=["POST"])
def analyze_test(test_id):
    """Analyze all solutions for a specific test asynchronously."""
    # Get assessment
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Get solutions
    solutions = db_service.get_solutions_by_test_id(test_id)
    if not solutions:
        return jsonify({"message": "No solutions found for this test"}), 404

    # Check if test is already being analyzed
    existing_jobs = db_service.get_all_analysis_jobs()
    for job in existing_jobs:
        if (job["job_type"] == "test" and
            job["job_data"].get("test_id") == test_id and
            job["status"] in ["pending", "running"]):
            return jsonify({
                "message": "Test is already being analyzed",
                "job_id": job["job_id"],
                "status": job["status"]
            })

    # Start analysis job
    job_id = background_worker.start_analysis_job("test", {"test_id": test_id})

    return jsonify({
        "message": "Test analysis started",
        "job_id": job_id
    })

@app.route("/api/analyze/all", methods=["POST"])
def analyze_all():
    """Analyze all unprocessed solutions asynchronously."""
    # Check if there's already an "all" analysis job running
    existing_jobs = db_service.get_all_analysis_jobs()
    for job in existing_jobs:
        if (job["job_type"] == "all" and job["status"] in ["pending", "running"]):
            return jsonify({
                "message": "All solutions are already being analyzed",
                "job_id": job["job_id"],
                "status": job["status"]
            })

    # Start analysis job
    job_id = background_worker.start_analysis_job("all", {})

    return jsonify({
        "message": "Analysis of all unprocessed solutions started",
        "job_id": job_id
    })

# Analysis job endpoints

@app.route("/api/analysis/jobs", methods=["GET"])
def get_analysis_jobs():
    """Get all analysis jobs."""
    jobs = db_service.get_all_analysis_jobs()
    return jsonify(jobs)

@app.route("/api/analysis/jobs/<job_id>", methods=["GET"])
def get_analysis_job(job_id):
    """Get a specific analysis job."""
    job = background_worker.get_job_status(job_id)

    if not job:
        return jsonify({"message": "Analysis job not found"}), 404

    return jsonify(job)

@app.route("/api/analysis/jobs/<job_id>/logs", methods=["GET"])
def get_analysis_job_logs(job_id):
    """Get logs for a specific analysis job."""
    logs = background_worker.get_job_logs(job_id)

    if not logs:
        return jsonify([])

    # Convert any ObjectId to string
    for log in logs:
        for key, value in list(log.items()):
            if hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool, type(None))):
                log[key] = str(value)

    return jsonify(logs)

# Assessment submission endpoints

@app.route("/api/assessments/<test_id>/start", methods=["POST"])
def start_assessment(test_id):
    """Start an assessment for a candidate."""
    data = request.get_json()

    # Validate required fields
    if "candidate_id" not in data:
        return jsonify({"message": "Missing required field: candidate_id"}), 400

    candidate_id = data["candidate_id"]

    # Check if assessment exists
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Check if solution already exists
    existing_solution = db_service.get_solution_by_test_and_candidate(test_id, candidate_id)
    if existing_solution:
        return jsonify({
            "message": "Assessment already started for this candidate",
            "solution_id": existing_solution["solution_id"],
            "started_at": existing_solution["started_at"]
        }), 409

    # Create new solution
    solution_id = f"{test_id}-{candidate_id}-{uuid.uuid4().hex[:8]}"
    solution = {
        "solution_id": solution_id,
        "test_id": test_id,
        "candidate_id": candidate_id,
        "answers": [],
        "coding_answers": [],
        "started_at": datetime.now().isoformat(),
        "completed_at": None,
        "time_taken": None
    }
    db_service.store_solution(solution)

    return jsonify({
        "message": "Assessment started successfully",
        "solution_id": solution_id,
        "started_at": solution["started_at"]
    })

@app.route("/api/assessments/<test_id>/submit/coding", methods=["POST"])
def submit_coding_answer(test_id):
    """Submit a coding answer for an assessment."""
    data = request.get_json()

    # Validate required fields
    required_fields = ["candidate_id", "question_id", "code", "language"]
    for field in required_fields:
        if field not in data:
            return jsonify({"message": f"Missing required field: {field}"}), 400

    candidate_id = data["candidate_id"]
    question_id = data["question_id"]
    code = data["code"]
    language = data["language"]
    execution_time = data.get("execution_time", 0.0)
    memory_usage = data.get("memory_usage", 0)

    # Check if assessment exists
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Find or create solution
    solution = db_service.get_solution_by_test_and_candidate(test_id, candidate_id)

    if not solution:
        # Create new solution
        solution_id = f"{test_id}-{candidate_id}-{uuid.uuid4().hex[:8]}"
        solution = {
            "solution_id": solution_id,
            "test_id": test_id,
            "candidate_id": candidate_id,
            "answers": [],
            "coding_answers": [],
            "started_at": datetime.now().isoformat(),
            "completed_at": None,
            "time_taken": None
        }
        db_service.store_solution(solution)
    else:
        solution_id = solution["solution_id"]

    # For coding questions, we allow multiple submissions (user can update their code)
    # Remove existing coding answer for this question if it exists
    solution["coding_answers"] = [
        answer for answer in solution.get("coding_answers", [])
        if answer["question_id"] != question_id
    ]

    # Create new coding answer
    new_coding_answer = {
        "question_id": question_id,
        "code": code,
        "language": language,
        "execution_time": execution_time,
        "memory_usage": memory_usage,
        "submitted_at": datetime.now().isoformat()
    }

    # Add coding answer to solution
    solution["coding_answers"].append(new_coding_answer)

    # Update solution in database
    db_service.update_solution(solution_id, solution)

    return jsonify({
        "message": "Coding answer submitted successfully",
        "solution_id": solution_id,
        "question_id": question_id,
        "coding_answer": new_coding_answer
    })

@app.route("/api/assessments/<test_id>/submit/complete", methods=["POST"])
def complete_assessment(test_id):
    """Complete an assessment with all regular answers for a candidate."""
    data = request.get_json()

    # Validate required fields
    required_fields = ["candidate_id", "answers"]
    for field in required_fields:
        if field not in data:
            return jsonify({"message": f"Missing required field: {field}"}), 400

    candidate_id = data["candidate_id"]
    answers = data["answers"]

    # Validate answers format
    if not isinstance(answers, list):
        return jsonify({"message": "answers must be a list"}), 400

    # Validate each answer
    for i, answer in enumerate(answers):
        required_answer_fields = ["question_id", "answer_type", "value"]
        for field in required_answer_fields:
            if field not in answer:
                return jsonify({"message": f"Missing required field '{field}' in answer {i+1}"}), 400

        # Validate answer type
        if answer["answer_type"] not in ["MCQ", "OPEN_ENDED"]:
            return jsonify({"message": f"Invalid answer_type in answer {i+1}. Must be 'MCQ' or 'OPEN_ENDED'"}), 400

    # Check if assessment exists
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Find solution
    solution = db_service.get_solution_by_test_and_candidate(test_id, candidate_id)
    if not solution:
        return jsonify({"message": "No solution found for this candidate. Please start the assessment first."}), 404

    # Check if assessment is already completed
    if solution.get("completed_at"):
        return jsonify({
            "message": "Assessment already completed",
            "solution_id": solution["solution_id"],
            "completed_at": solution["completed_at"]
        }), 409

    # Add timestamps to answers and store them
    submitted_at = datetime.now().isoformat()
    processed_answers = []

    for answer in answers:
        processed_answer = {
            "question_id": answer["question_id"],
            "answer_type": answer["answer_type"],
            "value": answer["value"],
            "submitted_at": submitted_at
        }
        processed_answers.append(processed_answer)

    # Calculate time taken
    started_at = datetime.fromisoformat(solution["started_at"])
    completed_at = datetime.now()
    time_taken = int((completed_at - started_at).total_seconds())

    # Update solution with all answers
    solution["answers"] = processed_answers
    solution["completed_at"] = completed_at.isoformat()
    solution["time_taken"] = time_taken

    # Update solution in database
    db_service.update_solution(solution["solution_id"], solution)

    return jsonify({
        "message": "Assessment completed successfully",
        "solution_id": solution["solution_id"],
        "completed_at": solution["completed_at"],
        "time_taken": time_taken,
        "answers_submitted": len(processed_answers)
    })

@app.route("/api/assessments/<test_id>/candidate/<candidate_id>/solution", methods=["GET"])
def get_candidate_solution(test_id, candidate_id):
    """Get the current solution for a candidate in an assessment."""
    # Check if assessment exists
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Find solution
    solution = db_service.get_solution_by_test_and_candidate(test_id, candidate_id)
    if not solution:
        return jsonify({"message": "No solution found for this candidate"}), 404

    return jsonify(solution)

@app.route("/api/assessments/<test_id>/test-code", methods=["POST"])
def test_code(test_id):
    """Test coding solution using Docker."""
    data = request.get_json()

    # Validate required fields
    required_fields = ["question_id", "code", "language"]
    for field in required_fields:
        if field not in data:
            return jsonify({"message": f"Missing required field: {field}"}), 400

    question_id = data["question_id"]
    code = data["code"]
    language = data["language"]

    # Check if assessment exists
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Find the coding question
    coding_question = None
    for cq in assessment.get("codingQuestions", []):
        if str(cq.get("order")) == str(question_id):
            coding_question = cq
            break

    if not coding_question:
        return jsonify({"message": "Coding question not found"}), 404

    # Validate language
    if coding_question.get("language") != language:
        return jsonify({
            "message": f"Invalid language. Expected {coding_question.get('language')}, got {language}"
        }), 400

    try:
        # Import and use the code execution service
        from server.services.code_execution_service import CodeExecutionService
        code_executor = CodeExecutionService()

        # Execute the code with test cases
        test_cases = coding_question.get("testCases", [])
        # results = code_executor.execute_code_with_tests(code, language, test_cases)
        results = code_executor.execute_code(
            code=code,
            language=language,
            test_cases=test_cases,
            timeout=15  # 15 seconds timeout
        )

        return jsonify({
            "message": "Code tested successfully",
            "results": results,
            "question_id": question_id,
            "language": language
        })

    except Exception as e:
        logger.error(f"Error testing code: {str(e)}")
        return jsonify({
            "message": "Error testing code",
            "error": str(e)
        }), 500

# Report endpoints

@app.route("/api/reports", methods=["GET"])
def get_reports():
    """Get all reports."""
    reports = db_service.get_all_reports()
    return jsonify(reports)

@app.route("/api/reports/<report_id>", methods=["GET"])
def get_report(report_id):
    """Get a specific report."""
    report = db_service.get_report_by_id(report_id)

    if not report:
        return jsonify({"message": "Report not found"}), 404

    return jsonify(report)

@app.route("/api/reports/test/<test_id>", methods=["GET"])
def get_report_by_test(test_id):
    """Get report for a specific test."""
    report = db_service.get_report_by_test_id(test_id)

    if not report:
        return jsonify({"message": "Report not found"}), 404

    return jsonify(report)

@app.route("/api/reports/generate/<test_id>", methods=["POST"])
def generate_report(test_id):
    """Generate report for a specific test."""
    # Check if test exists
    assessment = db_service.get_assessment_by_id(test_id)
    if not assessment:
        return jsonify({"message": "Assessment not found"}), 404

    # Generate report
    report_id = reporting_service.generate_test_report(test_id)

    if not report_id:
        return jsonify({"message": "Failed to generate report"}), 500

    return jsonify({
        "message": "Report generated successfully",
        "report_id": report_id
    })

@app.route("/api/reports/generate/all", methods=["POST"])
def generate_all_reports():
    """Generate reports for all tests with analyzed solutions."""
    # Get all test IDs
    test_ids = db_service.get_all_test_ids()

    # Generate reports
    report_ids = []
    for test_id in test_ids:
        report_id = reporting_service.generate_test_report(test_id)
        if report_id:
            report_ids.append(report_id)

    return jsonify({
        "message": f"Generated {len(report_ids)} reports",
        "report_ids": report_ids
    })

# Swagger JSON endpoint
@app.route('/api/swagger.json')
def swagger_json():
    """Return the Swagger specification as JSON."""
    return jsonify(spec.to_dict())

# Main entry point
if __name__ == "__main__":
    port = int(os.getenv("PORT", 5002))
    debug = os.getenv("DEBUG", "False").lower() == "true"

    app.run(host="0.0.0.0", port=port, debug=debug)
